/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
header {
    text-align: center;
    margin-bottom: 2rem;
    padding: 2rem 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    font-weight: 300;
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Main Content */
main {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* Section Styles */
section {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
}

section:hover {
    transform: translateY(-2px);
}

/* Upload Section */
.upload-area {
    border: 2px dashed #ddd;
    border-radius: 10px;
    padding: 3rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: #667eea;
    background-color: #f8f9ff;
}

.upload-area.dragover {
    border-color: #667eea;
    background-color: #f0f4ff;
    transform: scale(1.02);
}

.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.upload-icon {
    font-size: 3rem;
    opacity: 0.6;
}

.upload-content h3 {
    color: #333;
    margin-bottom: 0.5rem;
}

.upload-content p {
    color: #666;
    margin-bottom: 1rem;
}

/* File Info */
.file-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin-top: 1rem;
}

.file-details {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.file-name {
    font-weight: 600;
    color: #333;
}

.file-size {
    font-size: 0.9rem;
    color: #666;
}

/* Button Styles */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background-color: #667eea;
    color: white;
}

.btn-primary:hover {
    background-color: #5a6fd8;
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
    transform: translateY(-1px);
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-success:hover {
    background-color: #218838;
    transform: translateY(-1px);
}

/* Processing Section */
.processing-controls {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 1rem;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.3s ease;
}

/* Preview Section */
.preview-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.preview-info {
    font-weight: 600;
    color: #333;
}

.table-container {
    overflow-x: auto;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

th, td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
    position: sticky;
    top: 0;
    z-index: 10;
}

tr:hover {
    background-color: #f8f9fa;
}

/* Source Section */
.source-controls {
    margin-bottom: 1rem;
}

.source-content {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 1rem;
    max-height: 400px;
    overflow-y: auto;
}

#xmlSource {
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    line-height: 1.4;
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* Error Section */
.error-content {
    text-align: center;
    color: #721c24;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 8px;
    padding: 2rem;
}

.error-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.error-content h3 {
    margin-bottom: 1rem;
}

.error-content p {
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
}

/* Footer */
footer {
    text-align: center;
    padding: 2rem 0;
    color: #666;
    border-top: 1px solid #eee;
    margin-top: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    section {
        padding: 1.5rem;
    }
    
    .upload-area {
        padding: 2rem 1rem;
    }
    
    .processing-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .preview-controls {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }
    
    .btn {
        width: 100%;
    }
}

@media (max-width: 480px) {
    header h1 {
        font-size: 1.5rem;
    }
    
    .upload-content h3 {
        font-size: 1.2rem;
    }
    
    .upload-icon {
        font-size: 2rem;
    }
}
