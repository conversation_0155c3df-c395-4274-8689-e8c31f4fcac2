// Global variables
let currentXMLData = null;
let currentCSVData = null;
let parsedTransactions = [];

// DOM elements
const fileInput = document.getElementById('fileInput');
const uploadArea = document.getElementById('uploadArea');
const fileInfo = document.getElementById('fileInfo');
const fileName = document.getElementById('fileName');
const fileSize = document.getElementById('fileSize');
const clearFileBtn = document.getElementById('clearFile');
const processingSection = document.getElementById('processingSection');
const convertBtn = document.getElementById('convertBtn');
const previewBtn = document.getElementById('previewBtn');
const progressBar = document.getElementById('progressBar');
const progressFill = document.getElementById('progressFill');
const previewSection = document.getElementById('previewSection');
const recordCount = document.getElementById('recordCount');
const downloadBtn = document.getElementById('downloadBtn');
const previewTable = document.getElementById('previewTable');
const tableHeader = document.getElementById('tableHeader');
const tableBody = document.getElementById('tableBody');
const sourceSection = document.getElementById('sourceSection');
const toggleSourceBtn = document.getElementById('toggleSource');
const sourceContent = document.getElementById('sourceContent');
const xmlSource = document.getElementById('xmlSource');
const errorSection = document.getElementById('errorSection');
const errorMessage = document.getElementById('errorMessage');
const dismissErrorBtn = document.getElementById('dismissError');

// Initialize event listeners
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
});

function initializeEventListeners() {
    // File input events
    fileInput.addEventListener('change', handleFileSelect);
    clearFileBtn.addEventListener('click', clearFile);
    
    // Drag and drop events
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleDrop);
    uploadArea.addEventListener('click', handleUploadAreaClick);
    
    // Processing events
    convertBtn.addEventListener('click', convertToCSV);
    previewBtn.addEventListener('click', previewData);
    downloadBtn.addEventListener('click', downloadCSV);
    
    // UI events
    toggleSourceBtn.addEventListener('click', toggleXMLSource);
    dismissErrorBtn.addEventListener('click', dismissError);
}

// File handling functions
function handleFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
        processFile(file);
    }
}

function handleDragOver(event) {
    event.preventDefault();
    uploadArea.classList.add('dragover');
}

function handleDragLeave(event) {
    event.preventDefault();
    uploadArea.classList.remove('dragover');
}

function handleDrop(event) {
    event.preventDefault();
    uploadArea.classList.remove('dragover');

    const files = event.dataTransfer.files;
    if (files.length > 0) {
        const file = files[0];
        processFile(file);
    }
}

function handleUploadAreaClick(event) {
    // Only trigger file input if the click wasn't on the button itself
    if (!event.target.closest('button')) {
        fileInput.click();
    }
}

function processFile(file) {
    // Validate file type
    if (!file.name.toLowerCase().endsWith('.xml')) {
        showError('Please select a valid XML file.');
        return;
    }
    
    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
        showError('File size too large. Please select a file smaller than 10MB.');
        return;
    }
    
    // Update UI
    fileName.textContent = file.name;
    fileSize.textContent = formatFileSize(file.size);
    fileInfo.style.display = 'flex';
    processingSection.style.display = 'block';
    
    // Read file content
    const reader = new FileReader();
    reader.onload = function(e) {
        currentXMLData = e.target.result;
        xmlSource.textContent = formatXML(currentXMLData);
        sourceSection.style.display = 'block';
    };
    reader.onerror = function() {
        showError('Error reading file. Please try again.');
    };
    reader.readAsText(file);
}

function clearFile() {
    fileInput.value = '';
    currentXMLData = null;
    currentCSVData = null;
    parsedTransactions = [];
    
    fileInfo.style.display = 'none';
    processingSection.style.display = 'none';
    previewSection.style.display = 'none';
    sourceSection.style.display = 'none';
    errorSection.style.display = 'none';
}

// XML parsing functions
function parseXMLTransactions(xmlString) {
    try {
        const parser = new DOMParser();
        const xmlDoc = parser.parseFromString(xmlString, 'text/xml');
        
        // Check for parsing errors
        const parserError = xmlDoc.querySelector('parsererror');
        if (parserError) {
            throw new Error('Invalid XML format');
        }
        
        const transactions = xmlDoc.querySelectorAll('transaction');
        const parsedData = [];
        
        transactions.forEach(transaction => {
            const transactionData = extractTransactionData(transaction);
            parsedData.push(transactionData);
        });
        
        return parsedData;
    } catch (error) {
        throw new Error(`XML parsing failed: ${error.message}`);
    }
}

function extractTransactionData(transactionElement) {
    const data = {};
    
    // Basic transaction info
    data.transactionDate = getElementText(transactionElement, 'transaction_date');
    data.transactionType = getElementText(transactionElement, 'type_of_bank_transaction');
    data.paymentMethod = getElementText(transactionElement, 'payment_method');
    
    // Transaction amount
    const amountElement = transactionElement.querySelector('transaction_amount');
    if (amountElement) {
        data.currency = getElementText(amountElement, 'currency');
        data.amount = getElementText(amountElement, 'amount');
        data.amountInWords = getElementText(amountElement, 'in_words');
    }
    
    data.transactionPurpose = getElementText(transactionElement, 'intention_of_transaction');
    
    // Participants
    const participants = transactionElement.querySelectorAll('participant');
    participants.forEach(participant => {
        const role = getElementText(participant, 'role');
        const individual = participant.querySelector('individual');
        
        if (individual && role === 'RECEIVER') {
            data.receiverName = getElementText(individual, 'name');
            data.receiverIdCard = getElementText(individual, 'id_card_no');
            data.receiverPhone = getElementText(individual, 'telephone_no');
            data.receiverAddress = extractAddress(individual.querySelector('address'));
        } else if (individual && role === 'CONDUCTOR') {
            data.conductorName = getElementText(individual, 'name');
            data.conductorIdCard = getElementText(individual, 'id_card_no');
            data.conductorPhone = getElementText(individual, 'telephone_no');
            data.conductorAddress = extractAddress(individual.querySelector('address'));
        }
    });
    
    // Bank accounts
    const bankAccounts = transactionElement.querySelectorAll('bank_account');
    bankAccounts.forEach(account => {
        const role = getElementText(account, 'role');
        const bank = account.querySelector('bank');
        
        if (role === 'DEBIT') {
            data.debitAccountHolder = getElementText(account, 'account_holder_name');
            data.debitAccountNo = getElementText(account, 'account_no');
            if (bank) {
                data.bankName = getElementText(bank, 'name');
                data.branchCode = getElementText(bank, 'branch');
            }
        } else if (role === 'CREDIT') {
            data.creditAccountHolder = getElementText(account, 'account_holder_name');
            data.creditAccountNo = getElementText(account, 'account_no');
        }
    });
    
    return data;
}

function getElementText(parent, tagName) {
    const element = parent.querySelector(tagName);
    return element ? element.textContent.trim() : '';
}

function extractAddress(addressElement) {
    if (!addressElement) return '';
    
    const parts = [];
    const buildingNo = getElementText(addressElement, 'building_no');
    const street = getElementText(addressElement, 'street');
    const city = getElementText(addressElement, 'city');
    const township = getElementText(addressElement, 'township');
    const state = getElementText(addressElement, 'state');
    const country = getElementText(addressElement, 'country');
    
    if (buildingNo) parts.push(buildingNo);
    if (street) parts.push(street);
    if (city) parts.push(city);
    if (township) parts.push(township);
    if (state) parts.push(state);
    if (country) parts.push(country);
    
    return parts.join(', ');
}

// CSV generation functions
function generateCSV(transactions) {
    if (!transactions || transactions.length === 0) {
        throw new Error('No transaction data to convert');
    }
    
    const headers = [
        'Transaction Date', 'Transaction Type', 'Payment Method', 'Currency', 'Amount',
        'Amount in Words', 'Transaction Purpose', 'Receiver Name', 'Receiver ID Card',
        'Receiver Phone', 'Receiver Address', 'Conductor Name', 'Conductor ID Card',
        'Conductor Phone', 'Conductor Address', 'Debit Account Holder', 'Debit Account No',
        'Credit Account Holder', 'Credit Account No', 'Bank Name', 'Branch Code'
    ];
    
    let csvContent = headers.map(header => escapeCSVField(header)).join(',') + '\n';
    
    transactions.forEach(transaction => {
        const row = [
            transaction.transactionDate || '',
            transaction.transactionType || '',
            transaction.paymentMethod || '',
            transaction.currency || '',
            transaction.amount || '',
            transaction.amountInWords || '',
            transaction.transactionPurpose || '',
            transaction.receiverName || '',
            transaction.receiverIdCard || '',
            transaction.receiverPhone || '',
            transaction.receiverAddress || '',
            transaction.conductorName || '',
            transaction.conductorIdCard || '',
            transaction.conductorPhone || '',
            transaction.conductorAddress || '',
            transaction.debitAccountHolder || '',
            transaction.debitAccountNo || '',
            transaction.creditAccountHolder || '',
            transaction.creditAccountNo || '',
            transaction.bankName || '',
            transaction.branchCode || ''
        ];
        
        csvContent += row.map(field => escapeCSVField(field)).join(',') + '\n';
    });
    
    return csvContent;
}

function escapeCSVField(field) {
    if (typeof field !== 'string') {
        field = String(field);
    }
    
    // If field contains comma, newline, or quote, wrap in quotes and escape internal quotes
    if (field.includes(',') || field.includes('\n') || field.includes('\r') || field.includes('"')) {
        return '"' + field.replace(/"/g, '""') + '"';
    }
    
    return field;
}

// UI functions
function convertToCSV() {
    if (!currentXMLData) {
        showError('Please select an XML file first.');
        return;
    }
    
    showProgress(true);
    updateProgress(20);
    
    try {
        // Parse XML
        updateProgress(50);
        parsedTransactions = parseXMLTransactions(currentXMLData);
        
        if (parsedTransactions.length === 0) {
            throw new Error('No transactions found in the XML file');
        }
        
        // Generate CSV
        updateProgress(80);
        currentCSVData = generateCSV(parsedTransactions);
        
        updateProgress(100);
        
        // Show success
        setTimeout(() => {
            showProgress(false);
            previewData();
        }, 500);
        
    } catch (error) {
        showProgress(false);
        showError(error.message);
    }
}

function previewData() {
    if (!parsedTransactions || parsedTransactions.length === 0) {
        showError('No data to preview. Please convert the XML file first.');
        return;
    }
    
    // Update record count
    recordCount.textContent = `${parsedTransactions.length} record${parsedTransactions.length !== 1 ? 's' : ''} found`;
    
    // Create table headers
    const headers = [
        'Transaction Date', 'Transaction Type', 'Payment Method', 'Currency', 'Amount',
        'Transaction Purpose', 'Receiver Name', 'Debit Account Holder', 'Credit Account Holder'
    ];
    
    tableHeader.innerHTML = '';
    const headerRow = document.createElement('tr');
    headers.forEach(header => {
        const th = document.createElement('th');
        th.textContent = header;
        headerRow.appendChild(th);
    });
    tableHeader.appendChild(headerRow);
    
    // Create table body (show first 10 records for preview)
    tableBody.innerHTML = '';
    const previewData = parsedTransactions.slice(0, 10);
    
    previewData.forEach(transaction => {
        const row = document.createElement('tr');
        const values = [
            transaction.transactionDate || '',
            transaction.transactionType || '',
            transaction.paymentMethod || '',
            transaction.currency || '',
            transaction.amount || '',
            transaction.transactionPurpose || '',
            transaction.receiverName || '',
            transaction.debitAccountHolder || '',
            transaction.creditAccountHolder || ''
        ];
        
        values.forEach(value => {
            const td = document.createElement('td');
            td.textContent = value;
            td.title = value; // Show full text on hover
            row.appendChild(td);
        });
        
        tableBody.appendChild(row);
    });
    
    previewSection.style.display = 'block';
}

function downloadCSV() {
    if (!currentCSVData) {
        showError('No CSV data available. Please convert the XML file first.');
        return;
    }
    
    const blob = new Blob([currentCSVData], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    
    link.setAttribute('href', url);
    link.setAttribute('download', 'transactions.csv');
    link.style.visibility = 'hidden';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
}

function toggleXMLSource() {
    if (sourceContent.style.display === 'none') {
        sourceContent.style.display = 'block';
        toggleSourceBtn.textContent = 'Hide XML Source';
    } else {
        sourceContent.style.display = 'none';
        toggleSourceBtn.textContent = 'Show XML Source';
    }
}

function showProgress(show) {
    progressBar.style.display = show ? 'block' : 'none';
    if (!show) {
        progressFill.style.width = '0%';
    }
}

function updateProgress(percentage) {
    progressFill.style.width = percentage + '%';
}

function showError(message) {
    errorMessage.textContent = message;
    errorSection.style.display = 'block';
}

function dismissError() {
    errorSection.style.display = 'none';
}

// Utility functions
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function formatXML(xmlString) {
    try {
        const parser = new DOMParser();
        const xmlDoc = parser.parseFromString(xmlString, 'text/xml');
        const serializer = new XMLSerializer();
        let formatted = serializer.serializeToString(xmlDoc);
        
        // Basic formatting (add line breaks and indentation)
        formatted = formatted.replace(/></g, '>\n<');
        
        return formatted;
    } catch (error) {
        return xmlString;
    }
}
