<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XML to CSV Converter</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>XML to CSV Converter</h1>
            <p>Convert XML transaction reports to CSV format</p>
        </header>

        <main>
            <!-- File Upload Section -->
            <section class="upload-section">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-content">
                        <div class="upload-icon">📁</div>
                        <h3>Upload XML File</h3>
                        <p>Drag and drop your XML file here or click to browse</p>
                        <input type="file" id="fileInput" accept=".xml" hidden>
                        <button type="button" class="btn btn-primary" onclick="event.stopPropagation(); document.getElementById('fileInput').click()">
                            Choose File
                        </button>
                    </div>
                </div>
                <div class="file-info" id="fileInfo" style="display: none;">
                    <div class="file-details">
                        <span class="file-name" id="fileName"></span>
                        <span class="file-size" id="fileSize"></span>
                    </div>
                    <button type="button" class="btn btn-secondary" id="clearFile">Clear</button>
                </div>
            </section>

            <!-- Processing Section -->
            <section class="processing-section" id="processingSection" style="display: none;">
                <div class="processing-controls">
                    <button type="button" class="btn btn-primary" id="convertBtn">
                        Convert to CSV
                    </button>
                    <button type="button" class="btn btn-secondary" id="previewBtn">
                        Preview Data
                    </button>
                </div>
                <div class="progress-bar" id="progressBar" style="display: none;">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
            </section>

            <!-- Preview Section -->
            <section class="preview-section" id="previewSection" style="display: none;">
                <h3>Data Preview</h3>
                <div class="preview-controls">
                    <div class="preview-info">
                        <span id="recordCount">0 records found</span>
                    </div>
                    <div class="preview-actions">
                        <button type="button" class="btn btn-success" id="downloadBtn">
                            Download CSV
                        </button>
                    </div>
                </div>
                <div class="table-container">
                    <table id="previewTable">
                        <thead id="tableHeader"></thead>
                        <tbody id="tableBody"></tbody>
                    </table>
                </div>
            </section>

            <!-- XML Source Section -->
            <section class="source-section" id="sourceSection" style="display: none;">
                <h3>Original XML Data</h3>
                <div class="source-controls">
                    <button type="button" class="btn btn-secondary" id="toggleSource">
                        Show XML Source
                    </button>
                </div>
                <div class="source-content" id="sourceContent" style="display: none;">
                    <pre id="xmlSource"></pre>
                </div>
            </section>

            <!-- Error Section -->
            <section class="error-section" id="errorSection" style="display: none;">
                <div class="error-content">
                    <div class="error-icon">⚠️</div>
                    <h3>Error Processing File</h3>
                    <p id="errorMessage"></p>
                    <button type="button" class="btn btn-secondary" id="dismissError">
                        Dismiss
                    </button>
                </div>
            </section>
        </main>

        <footer>
            <p>&copy; 2023 XML to CSV Converter. Built for transaction report processing.</p>
        </footer>
    </div>

    <script src="script.js"></script>
</body>
</html>
